// Pokemon Battle Game Configuration

const CONFIG = {
    // API Configuration
    API: {
        BASE_URL: 'https://pokeapi.co/api/v2',
        POKEMON_LIMIT: 151, // First generation only
        CACHE_DURATION: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
        REQUEST_DELAY: 100, // Delay between API requests to avoid rate limiting
    },

    // Battle Configuration
    BATTLE: {
        DEFAULT_LEVEL: 50,
        MAX_TEAM_SIZE: 6,
        MAX_MOVES_PER_POKEMON: 4,
        DAMAGE_VARIANCE: 0.15, // Random factor: 0.85 to 1.0
        CRITICAL_HIT_CHANCE: 0.0625, // 1/16 chance
        CRITICAL_HIT_MULTIPLIER: 1.5,
        STAB_MULTIPLIER: 1.5, // Same Type Attack Bonus
        
        // Status effect durations (in turns)
        STATUS_DURATION: {
            SLEEP: { min: 1, max: 3 },
            FREEZE: { min: 1, max: 4 },
            CONFUSION: { min: 2, max: 5 }
        },
        
        // Status effect damage percentages
        STATUS_DAMAGE: {
            POISON: 1/8,
            BURN: 1/8,
            TOXIC: 1/16 // Increases each turn
        }
    },

    // Type Effectiveness Chart
    TYPE_CHART: {
        normal: {
            rock: 0.5,
            ghost: 0,
            steel: 0.5
        },
        fire: {
            fire: 0.5,
            water: 0.5,
            grass: 2,
            ice: 2,
            bug: 2,
            rock: 0.5,
            dragon: 0.5,
            steel: 2
        },
        water: {
            fire: 2,
            water: 0.5,
            grass: 0.5,
            ground: 2,
            rock: 2,
            dragon: 0.5
        },
        electric: {
            water: 2,
            electric: 0.5,
            grass: 0.5,
            ground: 0,
            flying: 2,
            dragon: 0.5
        },
        grass: {
            fire: 0.5,
            water: 2,
            grass: 0.5,
            poison: 0.5,
            ground: 2,
            flying: 0.5,
            bug: 0.5,
            rock: 2,
            dragon: 0.5,
            steel: 0.5
        },
        ice: {
            fire: 0.5,
            water: 0.5,
            grass: 2,
            ice: 0.5,
            ground: 2,
            flying: 2,
            dragon: 2,
            steel: 0.5
        },
        fighting: {
            normal: 2,
            ice: 2,
            poison: 0.5,
            flying: 0.5,
            psychic: 0.5,
            bug: 0.5,
            rock: 2,
            ghost: 0,
            dark: 2,
            steel: 2,
            fairy: 0.5
        },
        poison: {
            grass: 2,
            poison: 0.5,
            ground: 0.5,
            rock: 0.5,
            ghost: 0.5,
            steel: 0,
            fairy: 2
        },
        ground: {
            fire: 2,
            electric: 2,
            grass: 0.5,
            poison: 2,
            flying: 0,
            bug: 0.5,
            rock: 2,
            steel: 2
        },
        flying: {
            electric: 0.5,
            grass: 2,
            ice: 0.5,
            fighting: 2,
            bug: 2,
            rock: 0.5,
            steel: 0.5
        },
        psychic: {
            fighting: 2,
            poison: 2,
            psychic: 0.5,
            dark: 0,
            steel: 0.5
        },
        bug: {
            fire: 0.5,
            grass: 2,
            fighting: 0.5,
            poison: 0.5,
            flying: 0.5,
            psychic: 2,
            ghost: 0.5,
            dark: 2,
            steel: 0.5,
            fairy: 0.5
        },
        rock: {
            fire: 2,
            ice: 2,
            fighting: 0.5,
            ground: 0.5,
            flying: 2,
            bug: 2,
            steel: 0.5
        },
        ghost: {
            normal: 0,
            psychic: 2,
            ghost: 2,
            dark: 0.5
        },
        dragon: {
            dragon: 2,
            steel: 0.5,
            fairy: 0
        },
        dark: {
            fighting: 0.5,
            psychic: 2,
            ghost: 2,
            dark: 0.5,
            fairy: 0.5
        },
        steel: {
            fire: 0.5,
            water: 0.5,
            electric: 0.5,
            ice: 2,
            rock: 2,
            steel: 0.5,
            fairy: 2
        },
        fairy: {
            fire: 0.5,
            fighting: 2,
            poison: 0.5,
            dragon: 2,
            dark: 2,
            steel: 0.5
        }
    },

    // AI Configuration
    AI: {
        BASIC: {
            name: 'Basic',
            description: 'Random move selection',
            switchChance: 0.1,
            statusMoveChance: 0.1
        },
        STANDARD: {
            name: 'Standard',
            description: 'Type-effective moves preferred',
            switchChance: 0.25,
            statusMoveChance: 0.2,
            typeEffectivenessWeight: 2.0
        },
        EXPERT: {
            name: 'Expert',
            description: 'Strategic decisions',
            switchChance: 0.4,
            statusMoveChance: 0.3,
            typeEffectivenessWeight: 3.0,
            damageCalculation: true,
            priorityMoveWeight: 1.5
        }
    },

    // UI Configuration
    UI: {
        ANIMATION_SPEED: 300,
        TEXT_SPEED: 50, // Characters per second
        HP_BAR_ANIMATION_SPEED: 500,
        BATTLE_TEXT_DELAY: 1000,
        AUTO_ADVANCE_DELAY: 2000
    },

    // Trainer Data
    TRAINERS: {
        BASIC: {
            name: 'Youngster Joey',
            sprite: 'assets/trainers/youngster.png',
            difficulty: 'BASIC',
            team: [
                { id: 19, level: 45 }, // Rattata
                { id: 16, level: 47 }, // Pidgey
                { id: 10, level: 46 }  // Caterpie
            ]
        },
        STANDARD: {
            name: 'Ace Trainer',
            sprite: 'assets/trainers/ace-trainer.png',
            difficulty: 'STANDARD',
            team: [
                { id: 25, level: 50 }, // Pikachu
                { id: 6, level: 52 },  // Charizard
                { id: 9, level: 51 }   // Blastoise
            ]
        },
        EXPERT: {
            name: 'Gym Leader',
            sprite: 'assets/trainers/gym-leader.png',
            difficulty: 'EXPERT',
            team: [
                { id: 3, level: 55 },  // Venusaur
                { id: 6, level: 55 },  // Charizard
                { id: 9, level: 55 },  // Blastoise
                { id: 25, level: 53 }, // Pikachu
                { id: 65, level: 54 }, // Alakazam
                { id: 68, level: 56 }  // Machamp
            ]
        }
    },

    // Player Default Team
    PLAYER_TEAM: [
        { id: 6, level: 50 },   // Charizard
        { id: 9, level: 50 },   // Blastoise
        { id: 3, level: 50 },   // Venusaur
        { id: 25, level: 50 },  // Pikachu
        { id: 65, level: 50 },  // Alakazam
        { id: 68, level: 50 }   // Machamp
    ],

    // Items Configuration
    ITEMS: {
        POTION: {
            name: 'Potion',
            type: 'heal',
            value: 20,
            description: 'Restores 20 HP'
        },
        SUPER_POTION: {
            name: 'Super Potion',
            type: 'heal',
            value: 50,
            description: 'Restores 50 HP'
        },
        HYPER_POTION: {
            name: 'Hyper Potion',
            type: 'heal',
            value: 200,
            description: 'Restores 200 HP'
        }
    },

    // Storage Keys
    STORAGE_KEYS: {
        POKEMON_DATA: 'pokemon_battle_data',
        SETTINGS: 'pokemon_battle_settings',
        SAVE_DATA: 'pokemon_battle_save'
    }
};

// Utility function to get type effectiveness
function getTypeEffectiveness(attackType, defenderTypes) {
    let effectiveness = 1;
    
    defenderTypes.forEach(defenderType => {
        const chart = CONFIG.TYPE_CHART[attackType];
        if (chart && chart[defenderType] !== undefined) {
            effectiveness *= chart[defenderType];
        }
    });
    
    return effectiveness;
}

// Utility function to calculate damage
function calculateDamage(attacker, defender, move) {
    const level = attacker.level;
    const power = move.power || 0;
    
    if (power === 0) return 0; // Status moves
    
    // Determine attack and defense stats
    const attackStat = move.damageClass === 'physical' ? attacker.stats.attack : attacker.stats.specialAttack;
    const defenseStat = move.damageClass === 'physical' ? defender.stats.defense : defender.stats.specialDefense;
    
    // Base damage calculation
    let damage = ((level * 2 / 5 + 2) * power * attackStat / defenseStat / 50 + 2);
    
    // Apply type effectiveness
    const effectiveness = getTypeEffectiveness(move.type, defender.types);
    damage *= effectiveness;
    
    // Apply STAB (Same Type Attack Bonus)
    if (attacker.types.includes(move.type)) {
        damage *= CONFIG.BATTLE.STAB_MULTIPLIER;
    }
    
    // Apply random factor
    const randomFactor = 1 - CONFIG.BATTLE.DAMAGE_VARIANCE + (Math.random() * CONFIG.BATTLE.DAMAGE_VARIANCE * 2);
    damage *= randomFactor;
    
    // Critical hit check
    if (Math.random() < CONFIG.BATTLE.CRITICAL_HIT_CHANCE) {
        damage *= CONFIG.BATTLE.CRITICAL_HIT_MULTIPLIER;
    }
    
    return Math.max(1, Math.floor(damage));
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CONFIG, getTypeEffectiveness, calculateDamage };
}
